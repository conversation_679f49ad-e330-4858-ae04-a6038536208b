<?php
/**
 * Admin Logout
 * Handles user logout and session cleanup
 */

define('CMS_INIT', true);
require_once '../../src/includes/init.php';

// Log logout activity if user is authenticated
if ($auth->isAuthenticated()) {
    $user = $auth->getCurrentUser();
    logActivity('admin_logout', 'User logged out', $user['id']);
}

// Logout user
$auth->logout();

// Add flash message for next page load
addFlashMessage('You have been logged out successfully.', 'success');

// Redirect to login page
redirect('login.php');
?>
