<?php
/**
 * Admin Dashboard
 * Main admin interface with overview and quick actions
 */

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once '../../src/includes/init.php';
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();

try {
    // Get statistics
    $stats = $postManager->getStatistics();
    
    // Get recent posts
    $recentPosts = $postManager->getRecentPosts(5);
    
} catch (Exception $e) {
    error_log("Error loading dashboard data: " . $e->getMessage());
    $stats = ['total' => 0, 'published' => 0, 'drafts' => 0];
    $recentPosts = [];
    addFlashMessage('Error loading dashboard data.', 'error');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Minimal CMS</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>
            
            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php" class="active">Dashboard</a></li>
                    <li><a href="posts.php">Posts</a></li>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-content">
        <div class="container">
            <?php echo displayFlashMessages(); ?>
            
            <div class="dashboard-welcome">
                <h2>Welcome back, <?php echo Security::escape($currentUser['username']); ?>!</h2>
                <p>Last login: <?php echo $currentUser['last_login'] ? formatDate($currentUser['last_login']) : 'First time'; ?></p>
            </div>
            
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">Total Posts</div>
                    <div class="stat-action">
                        <a href="posts.php">Manage Posts</a>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['published']; ?></div>
                    <div class="stat-label">Published</div>
                    <div class="stat-action">
                        <a href="posts.php?status=published">View Published</a>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['drafts']; ?></div>
                    <div class="stat-label">Drafts</div>
                    <div class="stat-action">
                        <a href="posts.php?status=draft">View Drafts</a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="dashboard-section">
                <h3>Quick Actions</h3>
                <div class="quick-actions">
                    <a href="posts.php?action=new" class="button button-primary">
                        Create New Post
                    </a>
                    <a href="posts.php" class="button">
                        Manage Posts
                    </a>
                    <a href="profile.php" class="button">
                        Edit Profile
                    </a>
                    <a href="../index.php" class="button" target="_blank">
                        View Site
                    </a>
                </div>
            </div>
            
            <!-- Recent Posts -->
            <div class="dashboard-section">
                <h3>Recent Posts</h3>
                <?php if (empty($recentPosts)): ?>
                    <div class="no-content">
                        <p>No posts created yet.</p>
                        <p><a href="posts.php?action=new">Create your first post</a></p>
                    </div>
                <?php else: ?>
                    <div class="recent-posts">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentPosts as $post): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo Security::escape($post['title']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $post['is_draft'] ? 'draft' : 'published'; ?>">
                                                <?php echo $post['is_draft'] ? 'Draft' : 'Published'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo timeAgo($post['updated_at']); ?>
                                        </td>
                                        <td class="actions">
                                            <a href="posts.php?action=edit&id=<?php echo $post['id']; ?>" 
                                               class="action-link">Edit</a>
                                            <?php if (!$post['is_draft']): ?>
                                                <a href="../post.php?id=<?php echo $post['id']; ?>" 
                                                   class="action-link" target="_blank">View</a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <div class="section-footer">
                            <a href="posts.php">View All Posts &raquo;</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- System Information -->
            <div class="dashboard-section">
                <h3>System Information</h3>
                <div class="system-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>PHP Version:</label>
                            <span><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="info-item">
                            <label>Database:</label>
                            <span>SQLite3</span>
                        </div>
                        <div class="info-item">
                            <label>Server Time:</label>
                            <span><?php echo date('Y-m-d H:i:s T'); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Your IP:</label>
                            <span><?php echo Security::getClientIP(); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
