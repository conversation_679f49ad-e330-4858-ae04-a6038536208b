<?php
/**
 * Posts Management
 * Handles CRUD operations for blog posts
 */

define('CMS_INIT', true);
require_once '../../src/includes/init.php';

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
$action = $_GET['action'] ?? 'list';
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        validateCSRF();
        
        $postAction = $_POST['action'] ?? '';
        
        switch ($postAction) {
            case 'create':
                $title = Security::sanitizeInput($_POST['title'] ?? '');
                $content = Security::sanitizeInput($_POST['content'] ?? '');
                $category = Security::sanitizeInput($_POST['category'] ?? 'tech');
                $isDraft = isset($_POST['is_draft']) ? true : false;

                $newPostId = $postManager->createPost($title, $content, $isDraft, $currentUser['id'], $category);
                
                logActivity('post_created', "Created post: {$title}", $currentUser['id']);
                addFlashMessage('Post created successfully!', 'success');
                redirect('posts.php?action=edit&id=' . $newPostId);
                break;
                
            case 'update':
                $updatePostId = (int)$_POST['post_id'];
                $title = Security::sanitizeInput($_POST['title'] ?? '');
                $content = Security::sanitizeInput($_POST['content'] ?? '');
                $category = Security::sanitizeInput($_POST['category'] ?? 'tech');
                $isDraft = isset($_POST['is_draft']) ? true : false;

                $postManager->updatePost($updatePostId, $title, $content, $isDraft, $category);
                
                logActivity('post_updated', "Updated post: {$title}", $currentUser['id']);
                addFlashMessage('Post updated successfully!', 'success');
                redirect('posts.php?action=edit&id=' . $updatePostId);
                break;
                
            case 'delete':
                $deletePostId = (int)$_POST['post_id'];
                $post = $postManager->getPost($deletePostId);
                
                if ($post) {
                    $postManager->deletePost($deletePostId);
                    logActivity('post_deleted', "Deleted post: {$post['title']}", $currentUser['id']);
                    addFlashMessage('Post deleted successfully!', 'success');
                } else {
                    addFlashMessage('Post not found.', 'error');
                }
                
                redirect('posts.php');
                break;
                
            case 'toggle_status':
                $togglePostId = (int)$_POST['post_id'];
                $post = $postManager->getPost($togglePostId);
                
                if ($post) {
                    $newStatus = $postManager->toggleDraftStatus($togglePostId);
                    $statusText = $newStatus ? 'draft' : 'published';
                    
                    logActivity('post_status_changed', "Changed post status to {$statusText}: {$post['title']}", $currentUser['id']);
                    addFlashMessage("Post status changed to {$statusText}!", 'success');
                } else {
                    addFlashMessage('Post not found.', 'error');
                }
                
                redirect('posts.php');
                break;
        }
        
    } catch (Exception $e) {
        addFlashMessage($e->getMessage(), 'error');
    }
}

// Handle different actions
switch ($action) {
    case 'new':
        $pageTitle = 'Create New Post';
        $post = null;
        break;
        
    case 'edit':
        if ($postId <= 0) {
            addFlashMessage('Invalid post ID.', 'error');
            redirect('posts.php');
        }
        
        try {
            $post = $postManager->getPost($postId);
            if (!$post) {
                addFlashMessage('Post not found.', 'error');
                redirect('posts.php');
            }
            $pageTitle = 'Edit Post: ' . $post['title'];
        } catch (Exception $e) {
            addFlashMessage('Error loading post.', 'error');
            redirect('posts.php');
        }
        break;
        
    default:
        $action = 'list';
        $pageTitle = 'Manage Posts';
        
        // Get posts with pagination
        $currentPage = getCurrentPage();
        $postsPerPage = 10;
        $status = $_GET['status'] ?? '';
        
        try {
            if ($status === 'published') {
                $result = $postManager->getAllPosts($currentPage, $postsPerPage, true);
            } elseif ($status === 'draft') {
                // Get only drafts
                $result = $postManager->getAllPosts($currentPage, $postsPerPage, false);
                $result['posts'] = array_filter($result['posts'], function($post) {
                    return $post['is_draft'];
                });
            } else {
                $result = $postManager->getAllPosts($currentPage, $postsPerPage, false);
            }
            
            $posts = $result['posts'];
            $totalPages = $result['totalPages'];
            $total = $result['total'];
            
        } catch (Exception $e) {
            error_log("Error loading posts: " . $e->getMessage());
            $posts = [];
            $totalPages = 0;
            $total = 0;
            addFlashMessage('Error loading posts.', 'error');
        }
        break;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Security::escape($pageTitle); ?> - Admin</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>
            
            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="posts.php" class="active">Posts</a></li>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-content">
        <div class="container">
            <?php echo displayFlashMessages(); ?>
            
            <div class="page-header">
                <h2><?php echo Security::escape($pageTitle); ?></h2>
                
                <?php if ($action === 'list'): ?>
                    <div class="page-actions">
                        <a href="posts.php?action=new" class="button button-primary">
                            Create New Post
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if ($action === 'list'): ?>
                <!-- Posts List View -->
                <div class="posts-filters">
                    <div class="filter-tabs">
                        <a href="posts.php" class="filter-tab <?php echo $status === '' ? 'active' : ''; ?>">
                            All Posts (<?php echo $total; ?>)
                        </a>
                        <a href="posts.php?status=published" class="filter-tab <?php echo $status === 'published' ? 'active' : ''; ?>">
                            Published
                        </a>
                        <a href="posts.php?status=draft" class="filter-tab <?php echo $status === 'draft' ? 'active' : ''; ?>">
                            Drafts
                        </a>
                    </div>
                </div>

                <?php if (empty($posts)): ?>
                    <div class="no-content">
                        <p>No posts found.</p>
                        <p><a href="posts.php?action=new">Create your first post</a></p>
                    </div>
                <?php else: ?>
                    <div class="posts-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Author</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($posts as $post): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo Security::escape($post['title']); ?></strong>
                                            <div class="post-excerpt">
                                                <?php echo Security::escape(truncateText(strip_tags($post['content']), 80)); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="category-badge category-<?php echo Security::escape($post['category'] ?? 'tech'); ?>">
                                                <?php echo Security::escape(ucfirst($post['category'] ?? 'tech')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $post['is_draft'] ? 'draft' : 'published'; ?>">
                                                <?php echo $post['is_draft'] ? 'Draft' : 'Published'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo Security::escape($post['author_name']); ?></td>
                                        <td><?php echo formatDate($post['created_at'], 'M j, Y'); ?></td>
                                        <td><?php echo timeAgo($post['updated_at']); ?></td>
                                        <td class="actions">
                                            <a href="posts.php?action=edit&id=<?php echo $post['id']; ?>"
                                               class="action-link">Edit</a>

                                            <?php if (!$post['is_draft']): ?>
                                                <a href="<?php echo '..' . generatePostUrl($post); ?>"
                                                   class="action-link" target="_blank">View</a>
                                            <?php endif; ?>

                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Toggle post status?')">
                                                <?php echo csrfTokenField(); ?>
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                                                <button type="submit" class="action-link">
                                                    <?php echo $post['is_draft'] ? 'Publish' : 'Unpublish'; ?>
                                                </button>
                                            </form>

                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this post?')">
                                                <?php echo csrfTokenField(); ?>
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                                                <button type="submit" class="action-link danger">Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination-container">
                            <?php
                            $baseUrl = 'posts.php' . ($status ? '?status=' . $status : '');
                            echo generatePagination($currentPage, $totalPages, $baseUrl);
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

            <?php else: ?>
                <!-- Post Form View -->
                <div class="post-form-container">
                    <form method="POST" action="posts.php" class="post-form">
                        <?php echo csrfTokenField(); ?>
                        <input type="hidden" name="action" value="<?php echo $post ? 'update' : 'create'; ?>">
                        <?php if ($post): ?>
                            <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="title">Title *</label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="<?php echo Security::escape($post['title'] ?? ''); ?>"
                                   required
                                   maxlength="255"
                                   class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="content">Content *</label>
                            <textarea id="content"
                                      name="content"
                                      required
                                      rows="15"
                                      class="form-control"><?php echo Security::escape($post['content'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="category">Category *</label>
                            <select id="category"
                                    name="category"
                                    required
                                    class="form-control">
                                <?php
                                $validCategories = Post::getValidCategories();
                                $selectedCategory = $post['category'] ?? 'tech';
                                foreach ($validCategories as $category):
                                ?>
                                    <option value="<?php echo Security::escape($category); ?>"
                                            <?php echo ($selectedCategory === $category) ? 'selected' : ''; ?>>
                                        <?php echo Security::escape(ucfirst($category)); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       name="is_draft"
                                       value="1"
                                       <?php echo ($post && $post['is_draft']) ? 'checked' : ''; ?>>
                                Save as draft
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="button button-primary">
                                <?php echo $post ? 'Update Post' : 'Create Post'; ?>
                            </button>
                            <a href="posts.php" class="button">Cancel</a>

                            <?php if ($post && !$post['is_draft']): ?>
                                <a href="<?php echo '..' . generatePostUrl($post); ?>"
                                   class="button" target="_blank">View Post</a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
