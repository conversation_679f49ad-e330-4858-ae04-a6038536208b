<?php
define('CMS_INIT', true);
require_once __DIR__ . '/../src/includes/init.php';

// Test the post lookup directly
$category = 'tech';
$slug = 'fdfdgfdggd';

echo "Testing post lookup:\n";
echo "Category: $category\n";
echo "Slug: $slug\n\n";

try {
    $post = $postManager->getPostBySlug($category, $slug, true);
    
    if ($post) {
        echo "Post found!\n";
        echo "ID: " . $post['id'] . "\n";
        echo "Title: " . $post['title'] . "\n";
        echo "Category: " . $post['category'] . "\n";
        echo "Slug: " . $post['slug'] . "\n";
        echo "Is Draft: " . $post['is_draft'] . "\n";
    } else {
        echo "Post not found!\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
