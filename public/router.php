<?php
/**
 * Router for PHP Development Server
 * Handles static files and routes dynamic requests
 *
 * Note: This file is only needed when using PHP's built-in development server.
 * For production, use app.php as the main entry point.
 */

// Get the requested URI
$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);

// Debug: Log the request
error_log("router.php: Processing request for '$path'");
file_put_contents('/tmp/router-debug.log', "router.php: Processing request for '$path'\n", FILE_APPEND);

// Check if it's a file that exists
$filePath = __DIR__ . $path;
error_log("router.php: Checking file path '$filePath'");

if (file_exists($filePath) && is_file($filePath)) {
    error_log("router.php: File exists, checking extension");
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

    // Let PHP development server handle static files directly
    $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];
    if (in_array($extension, $staticExtensions)) {
        error_log("router.php: Static file, letting server handle");
        return false;
    }

    // Let PHP development server handle PHP files directly (except app.php)
    if ($extension === 'php' && basename($path) !== 'app.php') {
        error_log("router.php: PHP file, letting server handle");
        return false;
    }
}

// For routes that don't match existing files, use the server-agnostic router
error_log("router.php: No file match, routing to app.php");
require_once 'app.php';
?>
