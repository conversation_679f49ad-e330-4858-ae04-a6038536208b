<?php
/**
 * Router for PHP Development Server
 * Handles static files and routes dynamic requests
 *
 * Note: This file is only needed when using PHP's built-in development server.
 * For production, use app.php as the main entry point.
 */

// Get the requested URI
$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);

// Check if it's a static file that exists
$filePath = __DIR__ . $path;
if (file_exists($filePath) && is_file($filePath)) {
    // Check if it's a static asset (CSS, JS, images, etc.)
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
    $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];

    if (in_array($extension, $staticExtensions)) {
        // Let PHP development server handle static files directly
        return false;
    }
}

// For dynamic requests, use the server-agnostic router
require_once 'app.php';
?>
