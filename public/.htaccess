# This file is not used in development and production environments

# Minimal CMS Apache Configuration - Server-Agnostic Version
#
# IMPORTANT: Most functionality has been moved to PHP for server-agnostic compatibility.
# This file now contains only essential server-level rules that cannot be implemented in PHP.
#
# Converted to PHP:
# - Security headers → Security::setSecurityHeaders()
# - File access protection → Security::isFileAccessAllowed()
# - URL rewriting → Router class
# - Cache control → Router::serveStaticFile()
# - Compression → Response::compressOutput()
# - Error pages → Router::sendError()
#
# For server-agnostic deployment, route all requests through app.php
# See SERVER-AGNOSTIC-SETUP.md for configuration examples for different web servers.

# Enable rewrite engine
# REQUIRED: Cannot be implemented in PHP - this is a server module directive
RewriteEngine On

# Route all requests through the server-agnostic entry point
# This replaces all the specific URL rewriting rules that are now handled in PHP
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ app.php [QSA,L]

# Disable directory browsing
# SECURITY: Cannot be implemented in PHP - this prevents directory listing when no index file exists
# This is a server-level security feature that must be set at the Apache configuration level
Options -Indexes

# Disable server signature in error pages
# SECURITY: Cannot be implemented in PHP - this controls Apache's built-in error page signatures
# This prevents Apache from revealing server version information in default error pages
ServerSignature Off

# Optional: Force HTTPS redirect at server level
# PERFORMANCE: While this can be done in PHP, doing it at server level is more efficient
# Uncomment the following lines if you want server-level HTTPS enforcement:
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Optional: Server-level compression for better performance
# PERFORMANCE: While compression is handled in PHP, server-level compression is more efficient
# Uncomment if you prefer server-level compression over PHP compression:
# <IfModule mod_deflate.c>
#     AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css
#     AddOutputFilterByType DEFLATE application/xml application/xhtml+xml application/rss+xml
#     AddOutputFilterByType DEFLATE application/javascript application/x-javascript
#     AddOutputFilterByType DEFLATE image/svg+xml
# </IfModule>

# Optional: Server-level caching for static assets
# PERFORMANCE: While caching is handled in PHP, server-level caching is more efficient
# Uncomment if you prefer server-level caching over PHP caching:
# <IfModule mod_expires.c>
#     ExpiresActive On
#     ExpiresByType text/css "access plus 1 month"
#     ExpiresByType application/javascript "access plus 1 month"
#     ExpiresByType image/png "access plus 1 month"
#     ExpiresByType image/jpg "access plus 1 month"
#     ExpiresByType image/jpeg "access plus 1 month"
#     ExpiresByType image/gif "access plus 1 month"
#     ExpiresByType image/svg+xml "access plus 1 month"
#     ExpiresByType font/woff "access plus 1 month"
#     ExpiresByType font/woff2 "access plus 1 month"
# </IfModule>
