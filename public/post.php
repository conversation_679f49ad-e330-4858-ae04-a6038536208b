<?php
/**
 * Single Post View
 * Displays a single blog post
 */

define('CMS_INIT', true);
require_once '../src/includes/init.php';

// Get post ID from URL
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($postId <= 0) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

try {
    // Get the post (published only for public view)
    $post = $postManager->getPost($postId, true);
    
    if (!$post) {
        header('HTTP/1.0 404 Not Found');
        include '404.php';
        exit;
    }
    
} catch (Exception $e) {
    error_log("Error loading post: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    include '500.php';
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Security::escape($post['title']); ?> - Minimal CMS</title>
    <link rel="stylesheet" href="/assets/style.css">
    <meta name="description" content="<?php echo Security::escape(generateExcerpt($post['content'], 160)); ?>">
</head>
<body>
    <header class="site-header">
        <div class="container">
            <h1 class="site-title">
                <a href="/index.php">Minimal CMS</a>
            </h1>

            <nav class="main-nav">
                <ul>
                    <li><a href="/index.php">Home</a></li>
                    <li><a href="/admin/">Admin</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <nav class="breadcrumb">
                <a href="/index.php">Home</a> &raquo;
                <span><?php echo Security::escape($post['title']); ?></span>
            </nav>
            
            <article class="post-single">
                <header class="post-header">
                    <h1 class="post-title">
                        <?php echo Security::escape($post['title']); ?>
                    </h1>
                    
                    <div class="post-meta">
                        <span class="post-category">
                            <span class="category-badge category-<?php echo Security::escape($post['category'] ?? 'tech'); ?>">
                                <?php echo Security::escape(ucfirst($post['category'] ?? 'tech')); ?>
                            </span>
                        </span>
                        <span class="post-author">
                            By <?php echo Security::escape($post['author_name']); ?>
                        </span>
                        <span class="post-date">
                            Published <?php echo formatDate($post['created_at']); ?>
                        </span>
                        <?php if ($post['updated_at'] !== $post['created_at']): ?>
                            <span class="post-updated">
                                Last updated <?php echo formatDate($post['updated_at']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </header>
                
                <div class="post-content">
                    <?php 
                    if (containsHtml($post['content'])) {
                        // Content contains HTML, display as-is (already sanitized during input)
                        echo $post['content'];
                    } else {
                        // Plain text content, convert newlines to breaks
                        echo nl2br_safe($post['content']);
                    }
                    ?>
                </div>
                
                <footer class="post-footer">
                    <div class="post-actions">
                        <a href="/index.php" class="back-link">&laquo; Back to Posts</a>
                    </div>
                </footer>
            </article>
        </div>
    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Minimal CMS. Built with security in mind.</p>
        </div>
    </footer>
</body>
</html>
