<?php
// Debug router pattern matching

$categories = ['tech', 'gaming', 'film', 'serie'];
$pattern = "/^\/(" . implode('|', $categories) . ")\/([a-z0-9\-]+)\/?$/";

$testUrls = [
    '/tech/fdfdgfdggd/',
    '/tech/fdfdgfdggd',
    'tech/fdfdgfdggd/',
    'tech/fdfdgfdggd'
];

echo "Pattern: $pattern\n\n";

foreach ($testUrls as $url) {
    $matches = [];
    $result = preg_match($pattern, $url, $matches);
    echo "URL: '$url' -> Match: " . ($result ? 'YES' : 'NO');
    if ($result) {
        echo " -> Category: '{$matches[1]}', Slug: '{$matches[2]}'";
    }
    echo "\n";
}
?>
