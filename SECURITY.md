# Security Overview - Minimal CMS

This document outlines the comprehensive security measures implemented in the Minimal CMS to protect against common web application vulnerabilities.

## Security Architecture

### Defense in Depth
The CMS implements multiple layers of security controls:
1. **Input Validation**: All user input is validated and sanitized
2. **Output Encoding**: All output is properly escaped
3. **Authentication**: Secure login with rate limiting
4. **Authorization**: Proper access controls
5. **Session Management**: Secure session handling
6. **Database Security**: Prepared statements and proper configuration
7. **Infrastructure Security**: Security headers and server configuration

## Implemented Security Controls

### 1. SQL Injection Prevention

**Implementation**:
- All database queries use prepared statements via PDO
- No dynamic SQL construction with user input
- Parameterized queries for all CRUD operations

**Code Example**:
```php
// Secure query using prepared statements
$stmt = $this->db->query(
    "SELECT * FROM posts WHERE id = ? AND is_draft = ?",
    [$postId, 0]
);
```

**Files**: `src/classes/Database.php`, all model classes

### 2. Cross-Site Scripting (XSS) Prevention

**Implementation**:
- Input sanitization using `htmlspecialchars()` with proper flags
- Output escaping for all user-generated content
- Content Security Policy (CSP) headers

**Code Example**:
```php
// Input sanitization
public static function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Output escaping
echo Security::escape($userContent);
```

**Files**: `src/classes/Security.php`, all view files

### 3. Cross-Site Request Forgery (CSRF) Protection

**Implementation**:
- CSRF tokens generated for each session
- Token validation on all state-changing operations
- Tokens embedded in all forms

**Code Example**:
```php
// Token generation
public static function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Token validation
public static function validateCSRFToken($token) {
    return hash_equals($_SESSION['csrf_token'], $token);
}
```

**Files**: `src/classes/Security.php`, all forms

### 4. Authentication Security

**Implementation**:
- Password hashing using Argon2ID algorithm
- Rate limiting on login attempts (5 attempts per 15 minutes)
- Account lockout after failed attempts
- Secure password requirements

**Code Example**:
```php
// Password hashing
$passwordHash = password_hash($password, PASSWORD_ARGON2ID);

// Password verification
if (!password_verify($password, $user['password_hash'])) {
    throw new Exception('Invalid credentials');
}
```

**Files**: `src/classes/Auth.php`

### 5. Session Management

**Implementation**:
- HTTPOnly and Secure cookie flags
- Session ID regeneration
- Database-backed session storage
- Session timeout and cleanup
- SameSite cookie attribute

**Code Example**:
```php
// Secure session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
ini_set('session.cookie_samesite', 'Strict');
ini_set('session.use_strict_mode', 1);
```

**Files**: `src/classes/Session.php`

### 6. Input Validation

**Implementation**:
- Server-side validation for all inputs
- Length restrictions on all fields
- Type validation (email, numeric, etc.)
- Whitelist-based validation where applicable

**Code Example**:
```php
// Length validation
public static function validateLength($string, $min = 1, $max = 255) {
    $length = strlen($string);
    return $length >= $min && $length <= $max;
}

// Email validation
public static function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}
```

**Files**: `src/classes/Security.php`

### 7. Security Headers

**Implementation**:
- Content Security Policy (CSP)
- X-XSS-Protection
- X-Content-Type-Options
- X-Frame-Options
- Strict-Transport-Security (HTTPS)
- Referrer-Policy

**Code Example**:
```php
// Security headers
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header("Content-Security-Policy: default-src 'self'; ...");
```

**Files**: `src/classes/Security.php`, `public/.htaccess`

### 8. Rate Limiting

**Implementation**:
- Login attempt tracking by IP address
- Configurable attempt limits and time windows
- Automatic cleanup of old attempts
- Account lockout mechanism

**Code Example**:
```php
public static function checkRateLimit($ip, $maxAttempts = 5, $timeWindow = 900) {
    // Clean old attempts
    $db->query("DELETE FROM login_attempts WHERE attempted_at < datetime('now', '-{$timeWindow} seconds')");
    
    // Count recent attempts
    $attempts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM login_attempts 
         WHERE ip_address = ? AND success = 0 AND attempted_at > datetime('now', '-{$timeWindow} seconds')",
        [$ip]
    );
    
    return ($attempts['count'] ?? 0) < $maxAttempts;
}
```

**Files**: `src/classes/Security.php`

## Security Configuration

### Database Security
- SQLite database stored outside web root
- Foreign key constraints enabled
- Proper file permissions (644 for database file)

### File System Security
- Source code outside web root (`src/` directory)
- Database files outside web root (`database/` directory)
- Log files outside web root (`logs/` directory)
- `.htaccess` rules to prevent direct access

### Error Handling
- Custom error pages (404, 500)
- Error logging without exposing sensitive information
- Graceful degradation on errors

## Security Best Practices Implemented

### 1. Principle of Least Privilege
- Admin authentication required for all admin functions
- No unnecessary permissions granted
- Separate public and admin interfaces

### 2. Secure by Default
- CSRF protection enabled by default
- Secure session settings
- Input validation on all inputs
- Output escaping on all outputs

### 3. Fail Securely
- Authentication failures don't reveal user existence
- Errors logged but not exposed to users
- Graceful handling of security violations

### 4. Defense in Depth
- Multiple security controls for each threat
- Layered security approach
- No single point of failure

## Threat Model

### Threats Addressed

1. **SQL Injection**: Prevented through prepared statements
2. **XSS**: Prevented through input sanitization and output escaping
3. **CSRF**: Prevented through token validation
4. **Session Hijacking**: Mitigated through secure session management
5. **Brute Force**: Prevented through rate limiting
6. **Information Disclosure**: Prevented through proper error handling
7. **Clickjacking**: Prevented through X-Frame-Options header
8. **MIME Sniffing**: Prevented through X-Content-Type-Options header

### Security Assumptions

1. **Server Security**: Assumes underlying server is properly secured
2. **HTTPS**: Recommends HTTPS for production use
3. **File Permissions**: Assumes proper file system permissions
4. **PHP Security**: Assumes PHP is up-to-date and properly configured

## Security Testing

### Recommended Testing

1. **Input Validation Testing**:
   - Test all forms with malicious input
   - Verify length restrictions
   - Test special characters and encoding

2. **Authentication Testing**:
   - Test rate limiting functionality
   - Verify password requirements
   - Test session management

3. **Authorization Testing**:
   - Verify admin-only access controls
   - Test direct URL access to admin functions

4. **CSRF Testing**:
   - Test forms without CSRF tokens
   - Test with invalid tokens
   - Test token reuse

## Security Monitoring

### Logging
- All authentication attempts logged
- Failed login attempts tracked
- Administrative actions logged
- Error conditions logged

### Log Files
- `logs/error.log`: Application errors
- `logs/activity.log`: User activities and security events

### Monitoring Recommendations
1. Monitor failed login attempts
2. Watch for unusual activity patterns
3. Regular log review
4. Set up alerts for security events

## Incident Response

### Security Incident Handling
1. **Detection**: Monitor logs for suspicious activity
2. **Containment**: Lock affected accounts if necessary
3. **Investigation**: Review logs and determine scope
4. **Recovery**: Restore from backups if needed
5. **Lessons Learned**: Update security measures

### Emergency Procedures
1. **Suspected Breach**: Change all passwords immediately
2. **Database Compromise**: Restore from clean backup
3. **Code Compromise**: Review all code changes

## Security Maintenance

### Regular Tasks
1. **Update PHP**: Keep PHP version current
2. **Review Logs**: Regular log analysis
3. **Backup Database**: Regular secure backups
4. **Security Review**: Periodic security assessment

### Security Updates
1. Monitor for new vulnerabilities
2. Apply security patches promptly
3. Review and update security configurations
4. Test security controls regularly

## Compliance Considerations

### Data Protection
- Minimal data collection
- Secure data storage
- Proper data handling procedures

### Privacy
- No unnecessary data collection
- Secure session management
- Proper data retention policies

## Conclusion

The Minimal CMS implements comprehensive security controls following industry best practices. The multi-layered security approach provides robust protection against common web application vulnerabilities while maintaining usability and performance.

Regular security reviews, monitoring, and updates are essential to maintain the security posture of the application.
