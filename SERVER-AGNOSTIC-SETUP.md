# Server-Agnostic CMS Setup

This document explains how the CMS has been converted from Apache `.htaccess` dependency to a server-agnostic implementation that works with any web server.

## Overview

The CMS now includes PHP-based implementations of all functionality previously handled by `.htaccess`, making it compatible with Apache, Nginx, Lighttpd, and other web servers.

## Key Changes

### 1. New PHP Classes

#### `Router` Class (`src/classes/Router.php`)
- Handles URL rewriting and routing
- Replaces Apache mod_rewrite functionality
- Manages SEO-friendly URLs
- Serves static files with proper headers

#### `Response` Class (`src/classes/Response.php`)
- Handles HTTP responses
- Manages compression (replaces mod_deflate)
- Sets cache headers (replaces mod_expires)
- Provides JSON and file download responses

#### `Config` Class (`src/includes/config.php`)
- Centralized configuration for server-agnostic settings
- Defines blocked files, MIME types, cache times
- Replaces hardcoded .htaccess values

#### Enhanced `Security` Class
- File access protection
- HTTPS enforcement
- Additional security measures

### 2. New Entry Point

#### `app.php` (`public/app.php`)
- Main entry point for all requests
- Initializes routing and response handling
- Replaces the need for .htaccess URL rewriting

## Converted .htaccess Functionality

### ✅ Security Headers (Lines 7-26)
**Before (.htaccess):**
```apache
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "DENY"
```

**After (PHP):**
```php
// In Security::setSecurityHeaders()
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
```

### ✅ File Access Protection (Lines 28-63)
**Before (.htaccess):**
```apache
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Require all denied
</FilesMatch>
```

**After (PHP):**
```php
// In Security::isFileAccessAllowed()
if (Config::isBlockedExtension($fileExtension)) {
    return false;
}
```

### ✅ URL Rewriting (Lines 90-106)
**Before (.htaccess):**
```apache
RewriteRule ^(tech|gaming|film|serie)/([a-z0-9\-]+)/?$ post-seo.php [L,QSA]
```

**After (PHP):**
```php
// In Router::init()
$categories = implode('|', Config::VALID_CATEGORIES);
self::addRoute('GET', "/^($categories)\/([a-z0-9\-]+)\/?$/", 'post-seo.php');
```

### ✅ Cache Control (Lines 66-75)
**Before (.htaccess):**
```apache
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
```

**After (PHP):**
```php
// In Router::serveStaticFile()
$cacheTime = Config::getCacheTime($extension);
header("Cache-Control: public, max-age=$cacheTime");
```

### ✅ Compression (Lines 78-88)
**Before (.htaccess):**
```apache
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE application/javascript
```

**After (PHP):**
```php
// In Response::compressOutput()
if (Config::isCompressible($contentType)) {
    header('Content-Encoding: gzip');
    return gzencode($buffer);
}
```

### ✅ Error Pages (Lines 44-45)
**Before (.htaccess):**
```apache
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php
```

**After (PHP):**
```php
// In Router::sendError()
if (file_exists(PUBLIC_PATH . '/404.php')) {
    require_once PUBLIC_PATH . '/404.php';
}
```

### ✅ HTTPS Enforcement (Lines 105-106)
**Before (.htaccess):**
```apache
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

**After (PHP):**
```php
// In Security::enforceHTTPS()
if ($force && (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on')) {
    $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header("Location: $redirectURL", true, 301);
    exit;
}
```

## Server Configuration

### Apache
Use `server-configs/apache.conf` or keep existing `.htaccess` but route through `app.php`:
```apache
RewriteRule ^(.*)$ app.php [QSA,L]
```

### Nginx
Use `server-configs/nginx.conf`:
```nginx
location / {
    try_files $uri $uri/ /app.php?$query_string;
}
```

### Lighttpd
Use `server-configs/lighttpd.conf`:
```lighttpd
url.rewrite-if-not-file = (
    "^/(.*)$" => "/app.php"
)
```

### PHP Built-in Server
For development:
```bash
php -S localhost:8000 -t public app.php
```

## Configuration Options

Edit `src/includes/config.php` to customize:

```php
const FORCE_HTTPS = true;           // Force HTTPS redirects
const ENABLE_COMPRESSION = true;    // Enable gzip compression
const ENABLE_CACHING = true;        // Enable static file caching
```

## Benefits

1. **Server Independence**: Works with any web server
2. **Centralized Configuration**: All settings in PHP files
3. **Better Security**: PHP-based access control
4. **Easier Deployment**: No server-specific configuration needed
5. **Development Friendly**: Works with PHP built-in server
6. **Maintainable**: All logic in version-controlled PHP files

## Migration Steps

1. **Backup** your current `.htaccess` file
2. **Update** your web server configuration to route requests through `app.php`
3. **Test** all functionality (URLs, static files, security)
4. **Configure** settings in `config.php` as needed
5. **Optional**: Remove or rename `.htaccess` file

## Testing

Test the following functionality:
- SEO URLs: `/tech/my-post/`
- Pagination: `/page/2/`
- Static files: CSS, JS, images
- Security: Try accessing `/src/`, `/.htaccess`
- Error pages: 404, 403, 500
- Admin area access
- File uploads (if applicable)

## Troubleshooting

### Static Files Not Loading
- Check web server configuration
- Verify file permissions
- Check `Router::serveStaticFile()` method

### URLs Not Working
- Verify web server routing to `app.php`
- Check `Router::init()` route definitions
- Ensure URL patterns match your content

### Security Issues
- Review `Security::isFileAccessAllowed()`
- Check `Config::BLOCKED_EXTENSIONS`
- Verify directory access restrictions

## Performance Notes

- Static files are served with proper cache headers
- Compression is enabled for text-based content
- Conditional requests (ETag, Last-Modified) are supported
- Consider using a reverse proxy (Nginx) for better static file performance in production
