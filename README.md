# Minimal CMS

A secure, lightweight content management system built with core PHP and SQLite3. Designed with security as the top priority while maintaining essential functionality for content management.

## Features

### Security Features
- **CSRF Protection**: All forms protected against Cross-Site Request Forgery
- **SQL Injection Prevention**: All database queries use prepared statements
- **XSS Protection**: Input sanitization and output escaping
- **Rate Limiting**: Login attempt throttling with account lockout
- **Secure Sessions**: HTTPOnly cookies, session regeneration, database session storage
- **Password Security**: Argon2ID hashing with strength validation
- **Security Headers**: CSP, XSS protection, clickjacking prevention
- **Input Validation**: Server-side validation for all user input

### Content Management
- **Post Management**: Create, edit, delete, and publish blog posts
- **Category System**: Organize posts into categories (tech, gaming, film, serie)
- **Draft System**: Save posts as drafts before publishing
- **Pagination**: Efficient pagination for post listings
- **Search Functionality**: Search through published posts
- **Admin Dashboard**: Comprehensive admin interface with statistics
- **SEO-Friendly URLs**: Clean URLs in format `/category/post-title/`
- **Title Uniqueness**: Prevents duplicate post titles

### Technical Features
- **Pure PHP**: No frameworks, minimal dependencies
- **SQLite3 Database**: Lightweight, file-based database
- **Responsive Design**: Mobile-friendly interface
- **SEO-Friendly URLs**: Clean URLs in format `/category/post-title/`
- **URL Slugs**: Automatic generation of URL-safe slugs from post titles
- **Backward Compatibility**: Old `/post.php?id=X` URLs still work
- **Error Handling**: Comprehensive error logging and user-friendly error pages

## Requirements

- PHP 7.4 or higher
- SQLite3 extension enabled
- Apache web server (recommended)
- Write permissions for database and logs directories

## Installation

### 1. Download and Extract
```bash
# Clone or download the CMS files to your web directory
git clone <repository-url> /path/to/your/webroot
# or extract the ZIP file to your web directory
```

### 2. Set Permissions
```bash
# Make sure the web server can write to these directories
chmod 755 database logs
chmod 644 database/cms.db  # After installation
```

### 3. Run Installation
1. Navigate to your website in a browser
2. You'll be automatically redirected to `install.php`
3. The installation script will:
   - Create the SQLite database
   - Set up the required tables
   - Create the default admin user
   - Display installation success message

### 4. Security Setup
1. **Change default admin password immediately**:
   - Username: `admin`
   - Password: `admin`
   - Go to Admin → Profile to change password

2. **Delete installation file**:
   ```bash
   rm install.php
   ```

3. **Configure web server** (if not using Apache):
   - Ensure database, src, and logs directories are not web-accessible
   - Set up appropriate security headers
   - Configure error pages

## Usage

### Development Server
For local development and testing, use the included start script:

```bash
# Start development server on default port (8000)
./start.sh

# Start on a specific port
./start.sh -p 8080

# Make server accessible from other devices on network
./start.sh -h 0.0.0.0

# View all options
./start.sh --help
```

The development server will:
- Start a PHP built-in server with proper document root
- Display server URL and admin panel URL
- Handle Ctrl+C gracefully for clean shutdown
- Automatically find available ports if the default is in use
- Show helpful status messages and error information

**Note**: The development server is only for local testing. Use a proper web server (Apache/Nginx) for production.

### Admin Access
- **Admin URL**: `yoursite.com/public/admin/`
- **Default Credentials**: admin / admin (change immediately!)

### Admin Features
- **Dashboard**: Overview with statistics and recent posts
- **Posts Management**: Create, edit, delete, and manage post status
- **Profile Management**: Change password and view account information
- **Security Monitoring**: View login attempts and security information

### Public Interface
- **Homepage**: `yoursite.com/public/` - Lists all published posts
- **Single Post (SEO)**: `yoursite.com/public/category/post-title/` - SEO-friendly URLs
- **Single Post (Legacy)**: `yoursite.com/public/post.php?id=X` - Backward compatibility
- **Search**: Search functionality available on homepage

### URL Structure
The CMS supports both modern SEO-friendly URLs and legacy URLs:

**SEO-Friendly URLs:**
- `yoursite.com/tech/my-awesome-post/` - Tech category post
- `yoursite.com/gaming/game-review/` - Gaming category post
- `yoursite.com/film/movie-review/` - Film category post
- `yoursite.com/serie/tv-show-review/` - Serie category post

**Legacy URLs (still supported):**
- `yoursite.com/post.php?id=1` - Direct post access by ID

## Directory Structure

```
/
├── public/                 # Web-accessible files
│   ├── index.php          # Public homepage
│   ├── post.php           # Single post view
│   ├── 404.php            # 404 error page
│   ├── 500.php            # 500 error page
│   ├── admin/             # Admin interface
│   │   ├── index.php      # Admin dashboard
│   │   ├── login.php      # Admin login
│   │   ├── logout.php     # Admin logout
│   │   ├── posts.php      # Post management
│   │   └── profile.php    # Profile management
│   ├── assets/            # Static assets
│   │   └── style.css      # Stylesheet
│   └── .htaccess          # Apache configuration
├── src/                   # Application source (not web-accessible)
│   ├── classes/           # PHP classes
│   │   ├── Database.php   # Database handler
│   │   ├── Auth.php       # Authentication
│   │   ├── Post.php       # Post management
│   │   ├── Security.php   # Security utilities
│   │   └── Session.php    # Session management
│   └── includes/          # Common includes
│       ├── init.php       # Application initialization
│       └── functions.php  # Utility functions
├── database/              # Database files (not web-accessible)
│   └── cms.db            # SQLite database
├── logs/                  # Log files (not web-accessible)
│   ├── error.log         # Error logs
│   └── activity.log      # Activity logs
├── install.php           # Installation script (delete after use)
├── README.md             # This file
└── SECURITY.md           # Security documentation
```

## Configuration

### Database Configuration
The database is automatically configured during installation. The SQLite file is stored in `database/cms.db`.

### Security Configuration
Security settings are configured in the `Security` class. Key settings include:
- CSRF token generation and validation
- Rate limiting parameters (5 attempts per 15 minutes)
- Password strength requirements
- Session security settings

### Customization
- **Styling**: Modify `public/assets/style.css`
- **Posts per page**: Change `$postsPerPage` variables in relevant files
- **Security settings**: Modify parameters in `src/classes/Security.php`

## Security Considerations

### Production Deployment
1. **Remove installation file**: Delete `install.php` after installation
2. **Change default credentials**: Update admin password immediately
3. **Enable HTTPS**: Use SSL/TLS encryption
4. **Regular backups**: Backup database and files regularly
5. **Update PHP**: Keep PHP version updated
6. **Monitor logs**: Regularly check error and activity logs
7. **File permissions**: Ensure proper file and directory permissions

### Security Features Implemented
- All database queries use prepared statements
- CSRF protection on all forms
- Input validation and output escaping
- Rate limiting on login attempts
- Secure session management
- Password hashing with Argon2ID
- Security headers (CSP, XSS protection, etc.)
- Activity logging for audit trails

## Troubleshooting

### Common Issues

**Installation fails**:
- Check PHP version (7.4+ required)
- Ensure SQLite3 extension is enabled
- Verify write permissions on database and logs directories

**Can't login**:
- Check if account is locked (wait 30 minutes or check logs)
- Verify database file exists and is readable
- Check error logs for specific issues

**Posts not displaying**:
- Ensure posts are published (not drafts)
- Check database connectivity
- Verify file permissions

**Permission errors**:
- Ensure web server has read/write access to database directory
- Check that logs directory is writable
- Verify .htaccess file is being processed

### Log Files
- **Error logs**: `logs/error.log`
- **Activity logs**: `logs/activity.log`

## Support

For issues, questions, or contributions:
1. Check the troubleshooting section
2. Review log files for error details
3. Ensure all security requirements are met
4. Verify proper installation steps were followed

## License

This project is released under the MIT License. See LICENSE file for details.

## Security Disclosure

If you discover a security vulnerability, please report it responsibly by contacting the maintainers directly rather than opening a public issue.
