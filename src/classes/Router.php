<?php
/**
 * Server-Agnostic Router Class
 * Handles URL rewriting and routing without relying on .htaccess
 */

class Router {
    private static $routes = [];
    private static $currentRoute = null;
    
    /**
     * Initialize the router with default routes
     */
    public static function init() {
        // Define default routes (equivalent to .htaccess rules)
        $categories = implode('|', Config::VALID_CATEGORIES);
        self::addRoute('GET', "/^\/($categories)\/([a-z0-9\-]+)\/?$/", 'post-seo.php');
        self::addRoute('GET', '/^\/page\/([0-9]+)\/?$/', 'index.php', ['page' => 1]);
        self::addRoute('GET', '/^\/post\.php$/', 'post.php'); // Backward compatibility
        self::addRoute('GET', '/^\/?$/', 'index.php'); // Root
    }
    
    /**
     * Add a route
     */
    public static function addRoute($method, $pattern, $target, $params = []) {
        self::$routes[] = [
            'method' => strtoupper($method),
            'pattern' => $pattern,
            'target' => $target,
            'params' => $params
        ];
    }
    
    /**
     * Route the current request
     */
    public static function route() {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = $_SERVER['REQUEST_URI'];
        $path = parse_url($uri, PHP_URL_PATH);
        
        // Security check - block access to protected files/directories
        if (!Security::isFileAccessAllowed($path)) {
            self::sendError(403, 'Access Denied');
            return;
        }
        
        // Check if it's a static file that exists
        $filePath = PUBLIC_PATH . $path;
        if (file_exists($filePath) && is_file($filePath)) {
            // Let the web server handle static files, or serve through PHP if needed
            if (self::isStaticFile($path)) {
                self::serveStaticFile($filePath);
                return;
            }
        }
        
        // Try to match routes
        foreach (self::$routes as $route) {
            if ($route['method'] !== $method && $route['method'] !== 'ANY') {
                continue;
            }

            // Debug: Log route matching attempt
            error_log("Router: Trying to match '$path' against pattern '{$route['pattern']}'");

            if (preg_match($route['pattern'], $path, $matches)) {
                error_log("Router: Route matched! Target: {$route['target']}");
                self::$currentRoute = $route;
                
                // Set parameters from URL matches
                if (count($matches) > 1) {
                    switch ($route['target']) {
                        case 'post-seo.php':
                            // Category and slug from URL
                            $_GET['category'] = $matches[1];
                            $_GET['slug'] = $matches[2];
                            break;
                        case 'index.php':
                            if (isset($route['params']['page'])) {
                                $_GET['page'] = $matches[1];
                            }
                            break;
                    }
                }
                
                // Include the target file
                $targetFile = PUBLIC_PATH . '/' . $route['target'];
                if (file_exists($targetFile)) {
                    // Make global variables available to included files
                    global $auth, $postManager;
                    require_once $targetFile;
                    return;
                }
            }
        }
        
        // No route matched - check if file exists
        if (file_exists($filePath) && is_file($filePath)) {
            // Make global variables available to included files
            global $auth, $postManager;
            require_once $filePath;
            return;
        }

        // Check if it's a directory and look for index.php
        if (file_exists($filePath) && is_dir($filePath)) {
            $indexFile = rtrim($filePath, '/') . '/index.php';
            if (file_exists($indexFile)) {
                // Make global variables available to included files
                global $auth, $postManager;
                require_once $indexFile;
                return;
            }
        }

        // 404 - Not found
        self::sendError(404, 'Not Found');
    }
    
    /**
     * Check if file is a static asset
     */
    private static function isStaticFile($path) {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];
        return in_array($extension, $staticExtensions);
    }
    
    /**
     * Serve static files with proper headers
     */
    private static function serveStaticFile($filePath) {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeType = Config::getMimeType($extension);
        $cacheTime = Config::getCacheTime($extension);

        // Set content type and cache headers
        header("Content-Type: $mimeType");

        if ($cacheTime > 0) {
            header("Cache-Control: public, max-age=$cacheTime");
            header("Expires: " . gmdate('D, d M Y H:i:s', time() + $cacheTime) . ' GMT');
        }

        header("Last-Modified: " . gmdate('D, d M Y H:i:s', filemtime($filePath)) . ' GMT');

        // Enable compression for compressible files
        if (Config::isCompressible($mimeType) && Config::ENABLE_COMPRESSION) {
            if (function_exists('gzencode') && strpos($_SERVER['HTTP_ACCEPT_ENCODING'] ?? '', 'gzip') !== false) {
                header('Content-Encoding: gzip');
                header('Vary: Accept-Encoding');
                echo gzencode(file_get_contents($filePath));
                return;
            }
        }

        readfile($filePath);
    }
    
    /**
     * Send error response
     */
    public static function sendError($code, $message = '') {
        http_response_code($code);
        
        switch ($code) {
            case 403:
                if (file_exists(PUBLIC_PATH . '/403.php')) {
                    require_once PUBLIC_PATH . '/403.php';
                } else {
                    echo "<h1>403 Forbidden</h1><p>Access denied.</p>";
                }
                break;
            case 404:
                if (file_exists(PUBLIC_PATH . '/404.php')) {
                    require_once PUBLIC_PATH . '/404.php';
                } else {
                    echo "<h1>404 Not Found</h1><p>Page not found.</p>";
                }
                break;
            case 500:
                if (file_exists(PUBLIC_PATH . '/500.php')) {
                    require_once PUBLIC_PATH . '/500.php';
                } else {
                    echo "<h1>500 Internal Server Error</h1><p>Server error.</p>";
                }
                break;
            default:
                echo "<h1>$code Error</h1><p>$message</p>";
        }
        exit;
    }
    
    /**
     * Get current route info
     */
    public static function getCurrentRoute() {
        return self::$currentRoute;
    }
    
    /**
     * Generate URL for a route
     */
    public static function url($path = '') {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        return $protocol . '://' . $host . '/' . ltrim($path, '/');
    }
}
?>
