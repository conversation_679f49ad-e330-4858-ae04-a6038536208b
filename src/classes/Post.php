<?php
/**
 * Post Management Class
 * Handles CRUD operations for blog posts
 */

class Post {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Get valid categories
     */
    public static function getValidCategories() {
        return ['tech', 'gaming', 'film', 'serie'];
    }
    
    /**
     * Get all posts with pagination
     */
    public function getAllPosts($page = 1, $perPage = 10, $publishedOnly = false) {
        $offset = ($page - 1) * $perPage;
        
        $whereClause = $publishedOnly ? "WHERE is_draft = 0" : "";
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM posts " . $whereClause;
        $total = $this->db->fetchOne($totalQuery)['total'];
        
        // Get posts
        $postsQuery = "
            SELECT p.*, u.username as author_name 
            FROM posts p 
            JOIN users u ON p.author_id = u.id 
            {$whereClause}
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        $posts = $this->db->fetchAll($postsQuery, [$perPage, $offset]);
        
        return [
            'posts' => $posts,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Get single post by ID
     */
    public function getPost($id, $publishedOnly = false) {
        $whereClause = $publishedOnly ? "AND is_draft = 0" : "";

        $query = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            WHERE p.id = ? {$whereClause}
        ";

        return $this->db->fetchOne($query, [$id]);
    }

    /**
     * Get single post by category and slug
     */
    public function getPostBySlug($category, $slug, $publishedOnly = false) {
        $whereClause = $publishedOnly ? "AND is_draft = 0" : "";

        $query = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            WHERE p.category = ? AND p.slug = ? {$whereClause}
        ";

        return $this->db->fetchOne($query, [$category, $slug]);
    }
    
    /**
     * Create new post
     */
    public function createPost($title, $content, $isDraft = true, $authorId = null, $category = 'tech') {
        // Validate input
        if (!Security::validateLength($title, 1, 255)) {
            throw new Exception('Title must be between 1 and 255 characters.');
        }

        if (!Security::validateLength($content, 1, 65535)) {
            throw new Exception('Content is required and must not exceed 65535 characters.');
        }

        // Check if title already exists
        $existingPost = $this->db->fetchOne(
            "SELECT id FROM posts WHERE title = ?",
            [$title]
        );
        if ($existingPost) {
            throw new Exception('A post with this title already exists. Please choose a different title.');
        }

        // Validate category
        $validCategories = ['tech', 'gaming', 'film', 'serie'];
        if (!in_array($category, $validCategories)) {
            throw new Exception('Invalid category. Must be one of: ' . implode(', ', $validCategories));
        }

        // Use current user if no author specified
        if ($authorId === null) {
            $authorId = Session::getUserId();
            if (!$authorId) {
                throw new Exception('Author ID is required.');
            }
        }

        // Sanitize input
        $title = Security::sanitizeInput($title);
        $content = Security::sanitizeInput($content);
        $category = Security::sanitizeInput($category);

        // Generate unique slug
        $slug = generateUniqueSlug($title);

        // Insert post
        $this->db->query(
            "INSERT INTO posts (title, content, is_draft, author_id, category, slug) VALUES (?, ?, ?, ?, ?, ?)",
            [$title, $content, $isDraft ? 1 : 0, $authorId, $category, $slug]
        );

        return $this->db->lastInsertId();
    }
    
    /**
     * Update existing post
     */
    public function updatePost($id, $title, $content, $isDraft = null, $category = null) {
        // Validate input
        if (!Security::validateLength($title, 1, 255)) {
            throw new Exception('Title must be between 1 and 255 characters.');
        }

        if (!Security::validateLength($content, 1, 65535)) {
            throw new Exception('Content is required and must not exceed 65535 characters.');
        }

        // Validate category if provided
        if ($category !== null) {
            $validCategories = ['tech', 'gaming', 'film', 'serie'];
            if (!in_array($category, $validCategories)) {
                throw new Exception('Invalid category. Must be one of: ' . implode(', ', $validCategories));
            }
        }

        // Check if post exists
        $existingPost = $this->getPost($id);
        if (!$existingPost) {
            throw new Exception('Post not found.');
        }

        // Check if title already exists (excluding current post)
        if ($title !== $existingPost['title']) {
            $duplicatePost = $this->db->fetchOne(
                "SELECT id FROM posts WHERE title = ? AND id != ?",
                [$title, $id]
            );
            if ($duplicatePost) {
                throw new Exception('A post with this title already exists. Please choose a different title.');
            }
        }

        // Sanitize input
        $title = Security::sanitizeInput($title);
        $content = Security::sanitizeInput($content);
        if ($category !== null) {
            $category = Security::sanitizeInput($category);
        }

        // Generate new slug if title changed
        $updateSlug = false;
        $newSlug = null;
        if ($title !== $existingPost['title']) {
            $newSlug = generateUniqueSlug($title, $id);
            $updateSlug = true;
        }

        // Build update query
        $params = [$title, $content];
        $query = "UPDATE posts SET title = ?, content = ?, updated_at = CURRENT_TIMESTAMP";

        if ($isDraft !== null) {
            $query .= ", is_draft = ?";
            $params[] = $isDraft ? 1 : 0;
        }

        if ($category !== null) {
            $query .= ", category = ?";
            $params[] = $category;
        }

        if ($updateSlug) {
            $query .= ", slug = ?";
            $params[] = $newSlug;
        }

        $query .= " WHERE id = ?";
        $params[] = $id;

        $this->db->query($query, $params);

        return true;
    }
    
    /**
     * Delete post
     */
    public function deletePost($id) {
        // Check if post exists
        $post = $this->getPost($id);
        if (!$post) {
            throw new Exception('Post not found.');
        }
        
        $this->db->query("DELETE FROM posts WHERE id = ?", [$id]);
        
        return true;
    }
    
    /**
     * Toggle post draft status
     */
    public function toggleDraftStatus($id) {
        $post = $this->getPost($id);
        if (!$post) {
            throw new Exception('Post not found.');
        }
        
        $newStatus = $post['is_draft'] ? 0 : 1;
        $this->db->query(
            "UPDATE posts SET is_draft = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            [$newStatus, $id]
        );
        
        return $newStatus;
    }
    
    /**
     * Get recent posts for dashboard
     */
    public function getRecentPosts($limit = 5) {
        return $this->db->fetchAll(
            "SELECT p.*, u.username as author_name 
             FROM posts p 
             JOIN users u ON p.author_id = u.id 
             ORDER BY p.updated_at DESC 
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get post statistics
     */
    public function getStatistics() {
        $stats = [];
        
        // Total posts
        $stats['total'] = $this->db->fetchOne("SELECT COUNT(*) as count FROM posts")['count'];
        
        // Published posts
        $stats['published'] = $this->db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE is_draft = 0")['count'];
        
        // Draft posts
        $stats['drafts'] = $this->db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE is_draft = 1")['count'];
        
        return $stats;
    }
    
    /**
     * Search posts
     */
    public function searchPosts($query, $publishedOnly = false, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $searchTerm = '%' . $query . '%';
        
        $whereClause = "WHERE (title LIKE ? OR content LIKE ?)";
        if ($publishedOnly) {
            $whereClause .= " AND is_draft = 0";
        }
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM posts " . $whereClause;
        $total = $this->db->fetchOne($totalQuery, [$searchTerm, $searchTerm])['total'];
        
        // Get posts
        $postsQuery = "
            SELECT p.*, u.username as author_name 
            FROM posts p 
            JOIN users u ON p.author_id = u.id 
            {$whereClause}
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        $posts = $this->db->fetchAll($postsQuery, [$searchTerm, $searchTerm, $perPage, $offset]);
        
        return [
            'posts' => $posts,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage),
            'query' => $query
        ];
    }
}
?>
