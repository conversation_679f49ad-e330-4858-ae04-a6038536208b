<?php
/**
 * Utility Functions
 * Common helper functions used throughout the application
 */

/**
 * Format date for display
 */
function formatDate($date, $format = 'F j, Y g:i A') {
    return date($format, strtotime($date));
}

/**
 * Truncate text to specified length
 */
function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Generate excerpt from content
 */
function generateExcerpt($content, $length = 200) {
    // Strip HTML tags
    $text = strip_tags($content);
    
    // Truncate to word boundary
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $text = substr($text, 0, $length);
    $lastSpace = strrpos($text, ' ');
    
    if ($lastSpace !== false) {
        $text = substr($text, 0, $lastSpace);
    }
    
    return $text . '...';
}

/**
 * Display flash messages
 */
function displayFlashMessages() {
    $messages = Session::get('flash_messages', []);
    if (empty($messages)) {
        return '';
    }
    
    $html = '';
    foreach ($messages as $type => $messageList) {
        foreach ($messageList as $message) {
            $html .= '<div class="alert alert-' . Security::escape($type) . '">';
            $html .= Security::escape($message);
            $html .= '</div>';
        }
    }
    
    // Clear messages after displaying
    Session::remove('flash_messages');
    
    return $html;
}

/**
 * Add flash message
 */
function addFlashMessage($message, $type = 'info') {
    $messages = Session::get('flash_messages', []);
    $messages[$type][] = $message;
    Session::set('flash_messages', $messages);
}

/**
 * Check if string contains HTML
 */
function containsHtml($string) {
    return $string !== strip_tags($string);
}

/**
 * Convert newlines to HTML breaks
 */
function nl2br_safe($string) {
    return nl2br(Security::escape($string));
}

/**
 * Generate CSRF token input field
 */
function csrfTokenField() {
    $token = Security::generateCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . Security::escape($token) . '">';
}

/**
 * Validate CSRF token from POST data
 */
function validateCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!Security::validateCSRFToken($token)) {
            throw new Exception('Invalid CSRF token. Please try again.');
        }
    }
}

/**
 * Get current page number from query string
 */
function getCurrentPage() {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    return max(1, $page);
}

/**
 * Generate URL with query parameters
 */
function buildUrl($baseUrl, $params = []) {
    if (empty($params)) {
        return $baseUrl;
    }
    
    $queryString = http_build_query($params);
    $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
    
    return $baseUrl . $separator . $queryString;
}

/**
 * Check if current page matches given path
 */
function isCurrentPage($path) {
    $currentPath = $_SERVER['REQUEST_URI'];
    $currentPath = strtok($currentPath, '?'); // Remove query string
    
    return $currentPath === $path || basename($currentPath) === basename($path);
}

/**
 * Generate navigation menu
 */
function generateNavigation($items, $currentPath = null) {
    if ($currentPath === null) {
        $currentPath = $_SERVER['REQUEST_URI'];
        $currentPath = strtok($currentPath, '?');
    }
    
    $html = '<nav class="navigation"><ul>';
    
    foreach ($items as $item) {
        $isActive = ($currentPath === $item['url']) ? ' class="active"' : '';
        $html .= '<li' . $isActive . '>';
        $html .= '<a href="' . Security::escape($item['url']) . '">';
        $html .= Security::escape($item['title']);
        $html .= '</a></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}

/**
 * Log activity (for audit trail)
 */
function logActivity($action, $details = '', $userId = null) {
    if ($userId === null) {
        $userId = Session::getUserId();
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $userId,
        'ip_address' => Security::getClientIP(),
        'action' => $action,
        'details' => $details,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    $logMessage = json_encode($logEntry) . "\n";
    error_log($logMessage, 3, LOGS_PATH . '/activity.log');
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 2097152) { // 2MB default
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed.');
    }
    
    if ($file['size'] > $maxSize) {
        throw new Exception('File size exceeds maximum allowed size.');
    }
    
    if (!empty($allowedTypes)) {
        $fileType = mime_content_type($file['tmp_name']);
        if (!in_array($fileType, $allowedTypes)) {
            throw new Exception('File type not allowed.');
        }
    }
    
    return true;
}

/**
 * Generate random filename
 */
function generateRandomFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $randomName = bin2hex(random_bytes(16));
    
    return $randomName . '.' . $extension;
}

/**
 * Clean and validate URL
 */
function cleanUrl($url) {
    $url = filter_var($url, FILTER_SANITIZE_URL);
    
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    return $url;
}

/**
 * Time ago helper
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) {
        return 'just now';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return formatDate($datetime, 'M j, Y');
    }
}

/**
 * Generate URL-friendly slug from title
 */
function generateSlug($title) {
    // Convert to lowercase
    $slug = strtolower($title);

    // Replace spaces with hyphens
    $slug = str_replace(' ', '-', $slug);

    // Remove special characters, keep only alphanumeric and hyphens
    $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);

    // Remove multiple consecutive hyphens
    $slug = preg_replace('/-+/', '-', $slug);

    // Remove leading and trailing hyphens
    $slug = trim($slug, '-');

    // Ensure slug is not empty
    if (empty($slug)) {
        $slug = 'post-' . time();
    }

    return $slug;
}

/**
 * Generate SEO-friendly URL for a post
 */
function generatePostUrl($post) {
    $category = strtolower($post['category'] ?? 'tech');
    $slug = $post['slug'] ?? generateSlug($post['title']);

    return "/{$category}/{$slug}/";
}

/**
 * Check if a slug is unique for a given post
 */
function isSlugUnique($slug, $excludePostId = null) {
    $db = Database::getInstance();

    $query = "SELECT id FROM posts WHERE slug = ?";
    $params = [$slug];

    if ($excludePostId !== null) {
        $query .= " AND id != ?";
        $params[] = $excludePostId;
    }

    $result = $db->fetchOne($query, $params);
    return $result === false;
}

/**
 * Generate unique slug for a post
 */
function generateUniqueSlug($title, $excludePostId = null) {
    $baseSlug = generateSlug($title);
    $slug = $baseSlug;
    $counter = 1;

    while (!isSlugUnique($slug, $excludePostId)) {
        $slug = $baseSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}
?>
